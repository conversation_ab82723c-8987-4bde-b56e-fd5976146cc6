import React, { useCallback, useState } from 'react';
import { DownloadIcon, GavelIcon, LightbulbIcon, ReportIcon, ShieldExclamationIcon, SparklesIcon } from './components/icons';
import { EVIDENCE_QUESTIONS } from './constants';
import { generateComprehensiveAnalysis, getAICritique, getAIKeyPoints } from './services/geminiService';
import { AnalysisResult, Answer, Question } from './types';

interface EvidenceInfo {
  name: string;
  description: string;
  type: string;
}

const App: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [evidenceInfo, setEvidenceInfo] = useState<EvidenceInfo>({
    name: '',
    description: '',
    type: 'Mobile Phone',
  });
  const [answers, setAnswers] = useState<Answer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const totalQuestions = EVIDENCE_QUESTIONS.length;
  const totalSteps = totalQuestions + 1; // +1 for the evidence definition step
  const isFinalStep = currentStep === totalSteps;
  const currentQuestion = currentStep > 0 && currentStep <= totalQuestions ? EVIDENCE_QUESTIONS[currentStep - 1] : null;
  
  const handleAnswerChange = useCallback((questionId: string, value: string) => {
    setAnswers(prevAnswers => {
      const existingAnswerIndex = prevAnswers.findIndex(a => a.questionId === questionId);
      if (existingAnswerIndex > -1) {
        const newAnswers = [...prevAnswers];
        newAnswers[existingAnswerIndex] = { questionId, value };
        return newAnswers;
      }
      return [...prevAnswers, { questionId, value }];
    });
  }, []);

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const handleGenerateReport = async () => {
    setIsLoading(true);
    setError(null);
    setAnalysis(null);
    try {
      if (!process.env.API_KEY || process.env.API_KEY === 'placeholder_api_key') {
          throw new Error("API_KEY is not configured. Please set the environment variable to generate a report.");
      }
      const result = await generateComprehensiveAnalysis(answers, evidenceInfo);
      setAnalysis(result);
    } catch (e) {
      if (e instanceof Error) {
        setError(e.message);
      } else {
        setError('An unknown error occurred.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentStep = () => {
    if (analysis) return <ReportView analysis={analysis} answers={answers} evidenceInfo={evidenceInfo} />;
    if (isLoading) return <LoadingSpinner />;
    if (error) return <ErrorDisplay message={error} onRetry={handleGenerateReport} isFinalStep={isFinalStep} />;
    
    if (currentStep === 0) {
        return <EvidenceDefinitionStep evidenceInfo={evidenceInfo} setEvidenceInfo={setEvidenceInfo} />
    }

    if (currentStep > 0 && !isFinalStep && currentQuestion) {
      const answer = answers.find(a => a.questionId === currentQuestion.id)?.value || '';
      return <WizardStep question={currentQuestion} answer={answer} onAnswerChange={handleAnswerChange} />;
    }
    
    if (isFinalStep) {
      return <FinalStep onGenerateReport={handleGenerateReport} />;
    }

    return null;
  };
  
  const handleStartOver = () => {
    setCurrentStep(0);
    setEvidenceInfo({ name: '', description: '', type: 'Mobile Phone' });
    setAnswers([]);
    setAnalysis(null);
    setError(null);
    setIsLoading(false);
  }

  return (
    <div className="min-h-screen bg-gray-950">
      {/* Header */}
      <header className="bg-gray-900 border-b border-gray-800 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-brand-primary rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" viewBox="0 0 45 45" xmlns="http://www.w3.org/2000/svg">
                  <rect x="5" y="25" width="35" height="8" rx="2" fill="currentColor" opacity="0.9"/>
                  <rect x="8" y="18" width="29" height="8" rx="2" fill="currentColor" opacity="0.7"/>
                  <rect x="11" y="11" width="23" height="8" rx="2" fill="currentColor" opacity="0.5"/>
                  <rect x="14" y="4" width="17" height="8" rx="2" fill="currentColor" opacity="0.3"/>
                  <circle cx="38" cy="29" r="3" fill="currentColor"/>
                  <path d="M36 29l2 2 4-4" stroke="rgba(13, 17, 23, 0.8)" strokeWidth="2" fill="none"/>
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">ProofStack</h1>
                <p className="text-gray-400 text-sm">Legal Technology Platform</p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {analysis && (
              <button
                onClick={handleStartOver}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
              >
                New Assessment
              </button>
            )}
          </div>
        </div>
      </header>

      {/* Main Dashboard */}
      <main className="max-w-7xl mx-auto p-6">
        {analysis ? (
          <ReportView analysis={analysis} answers={answers} evidenceInfo={evidenceInfo} />
        ) : (
          <DashboardView
            currentStep={currentStep}
            evidenceInfo={evidenceInfo}
            setEvidenceInfo={setEvidenceInfo}
            answers={answers}
            onAnswerChange={handleAnswerChange}
            onGenerateReport={handleGenerateReport}
            isLoading={isLoading}
            error={error}
            totalQuestions={totalQuestions}
            currentQuestion={currentQuestion}
            isFinalStep={isFinalStep}
          />
        )}
      </main>
    </div>
  );
};

// Dashboard View Component
interface DashboardViewProps {
  currentStep: number;
  evidenceInfo: EvidenceInfo;
  setEvidenceInfo: (info: EvidenceInfo) => void;
  answers: Answer[];
  onAnswerChange: (questionId: string, value: string) => void;
  onGenerateReport: () => void;
  isLoading: boolean;
  error: string | null;
  totalQuestions: number;
  currentQuestion: Question | null;
  isFinalStep: boolean;
}

const DashboardView: React.FC<DashboardViewProps> = ({
  currentStep,
  evidenceInfo,
  setEvidenceInfo,
  answers,
  onAnswerChange,
  onGenerateReport,
  isLoading,
  error,
  totalQuestions,
  currentQuestion,
  isFinalStep
}) => {
  const completedQuestions = answers.length;
  const progressPercentage = (completedQuestions / totalQuestions) * 100;

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorDisplay message={error} onRetry={onGenerateReport} isFinalStep={isFinalStep} />;

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="bg-gray-900 border border-gray-800 rounded-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-4">Digital Evidence Assessment</h2>
          <p className="text-sm text-gray-400 max-w-3xl mx-auto">
            Evaluate your digital evidence against legal standards of admissibility using AI-powered analysis
          </p>
        </div>

        {/* Progress Overview */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Assessment Progress</h3>
            <span className="text-sm font-medium text-gray-300">{completedQuestions} of {totalQuestions} completed</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
            <div
              className="h-2 rounded-full bg-brand-primary transition-all duration-500"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-400">
            <span>Evidence Definition</span>
            <span>Foundational Questions</span>
            <span>Daubert Analysis</span>
            <span>Generate Report</span>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex justify-center gap-4">
          {completedQuestions === totalQuestions ? (
            <button
              onClick={onGenerateReport}
              className="btn-primary px-8 py-3 rounded-lg font-semibold transition-colors duration-200"
            >
              <ReportIcon className="w-4 h-4 mr-2" />
              Generate Analysis Report
            </button>
          ) : (
            <div className="text-center">
              <p className="text-sm text-gray-400 mb-4">Complete all assessment questions to generate your report</p>
              <div className="flex justify-center gap-2">
                <div className="w-2 h-2 bg-brand-primary rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-brand-secondary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-brand-primary rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Evidence Information Panel */}
        <div className="lg:col-span-1">
          <EvidencePanel evidenceInfo={evidenceInfo} setEvidenceInfo={setEvidenceInfo} />
        </div>

        {/* Questions Panel */}
        <div className="lg:col-span-2">
          <QuestionsPanel
            answers={answers}
            onAnswerChange={onAnswerChange}
            totalQuestions={totalQuestions}
          />
        </div>
      </div>
    </div>
  );
};

// Evidence Panel Component
const EvidencePanel: React.FC<{
  evidenceInfo: EvidenceInfo;
  setEvidenceInfo: (info: EvidenceInfo) => void;
}> = ({ evidenceInfo, setEvidenceInfo }) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEvidenceInfo({ ...evidenceInfo, [name]: value });
  };

  return (
    <div className="card p-6 h-fit">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-brand-primary rounded-lg flex items-center justify-center">
          <ShieldExclamationIcon className="w-4 h-4 text-white" />
        </div>
        <h3 className="text-lg font-semibold text-white">Evidence Details</h3>
      </div>

      <div className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
            Evidence Identifier
          </label>
          <input
            type="text"
            name="name"
            id="name"
            value={evidenceInfo.name}
            onChange={handleInputChange}
            className="input-field w-full p-3 focus:ring-2 focus:ring-brand-primary transition-colors duration-200"
            placeholder="e.g., iPhone 13 from suspect's vehicle"
          />
        </div>

        <div>
          <label htmlFor="type" className="block text-sm font-medium text-gray-300 mb-2">
            Evidence Type
          </label>
          <select
            name="type"
            id="type"
            value={evidenceInfo.type}
            onChange={handleInputChange}
            className="input-field w-full p-3 focus:ring-2 focus:ring-brand-primary transition-colors duration-200"
          >
            <option value="Mobile Phone">Mobile Phone</option>
            <option value="Computer">Computer</option>
            <option value="Hard Drive">Hard Drive</option>
            <option value="USB Drive">USB Drive</option>
            <option value="CCTV Footage">CCTV Footage</option>
            <option value="Email">Email</option>
            <option value="Social Media">Social Media</option>
            <option value="Network Logs">Network Logs</option>
            <option value="Other">Other</option>
          </select>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
            Description
          </label>
          <textarea
            name="description"
            id="description"
            value={evidenceInfo.description}
            onChange={handleInputChange}
            rows={4}
            className="input-field w-full p-3 focus:ring-2 focus:ring-brand-primary transition-colors duration-200 resize-none"
            placeholder="Provide a detailed description of the evidence..."
          />
        </div>
      </div>
    </div>
  );
};

// Questions Panel Component
const QuestionsPanel: React.FC<{
  answers: Answer[];
  onAnswerChange: (questionId: string, value: string) => void;
  totalQuestions: number;
}> = ({ answers, onAnswerChange, totalQuestions }) => {
  const [activeTab, setActiveTab] = useState<'foundational' | 'daubert'>('foundational');

  const foundationalQuestions = EVIDENCE_QUESTIONS.filter(q => q.section === 'Foundational Admissibility');
  const daubertQuestions = EVIDENCE_QUESTIONS.filter(q => q.section === 'Daubert Standard');

  const getQuestionStatus = (questionId: string) => {
    const answer = answers.find(a => a.questionId === questionId);
    return answer && answer.value.trim() ? 'completed' : 'pending';
  };

  const renderQuestionCard = (question: Question) => {
    const answer = answers.find(a => a.questionId === question.id)?.value || '';
    const status = getQuestionStatus(question.id);

    return (
      <div key={question.id} className="card p-6 hover:border-brand-primary transition-colors duration-200">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <div className={`w-3 h-3 rounded-full ${status === 'completed' ? 'bg-green-500' : 'bg-gray-600'}`}></div>
              <h4 className="text-lg font-semibold text-white">{question.title}</h4>
            </div>
            <p className="text-sm text-gray-400 mb-4">{question.text}</p>
          </div>
        </div>

        <textarea
          value={answer}
          onChange={(e) => onAnswerChange(question.id, e.target.value)}
          className="input-field w-full h-32 p-3 focus:ring-2 focus:ring-brand-primary transition-colors duration-200 resize-none"
          placeholder="Provide your detailed response..."
        />

        <AIAssistantPanel question={question} answer={answer} onAnswerChange={onAnswerChange} />
      </div>
    );
  };

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">Assessment Questions</h3>
        <div className="flex bg-gray-800 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('foundational')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
              activeTab === 'foundational'
                ? 'bg-brand-primary text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Foundational ({foundationalQuestions.length})
          </button>
          <button
            onClick={() => setActiveTab('daubert')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
              activeTab === 'daubert'
                ? 'bg-brand-primary text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Daubert ({daubertQuestions.length})
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {activeTab === 'foundational'
          ? foundationalQuestions.map(renderQuestionCard)
          : daubertQuestions.map(renderQuestionCard)
        }
      </div>
    </div>
  );
};


const EvidenceDefinitionStep = ({ evidenceInfo, setEvidenceInfo }: { evidenceInfo: EvidenceInfo, setEvidenceInfo: (info: EvidenceInfo) => void }) => {
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setEvidenceInfo({ ...evidenceInfo, [name]: value });
    };

    return (
        <div>
            <h2 className="text-3xl font-bold text-slate-800 text-center">Define Your Evidence</h2>
            <p className="text-slate-600 mt-2 mb-6 text-center">First, provide some basic details about the digital evidence you are assessing.</p>
            <div className="space-y-4">
                <div>
                    <label htmlFor="name" className="block text-sm font-medium text-slate-700 mb-1">Evidence Name / Identifier</label>
                    <input
                        type="text"
                        name="name"
                        id="name"
                        value={evidenceInfo.name}
                        onChange={handleInputChange}
                        className="w-full p-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., iPhone 13 from suspect's vehicle"
                    />
                </div>
                <div>
                    <label htmlFor="type" className="block text-sm font-medium text-slate-700 mb-1">Type of Evidence</label>
                    <select
                        name="type"
                        id="type"
                        value={evidenceInfo.type}
                        onChange={handleInputChange}
                        className="w-full p-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                        <option>Mobile Phone</option>
                        <option>Laptop / Computer</option>
                        <option>Email Account</option>
                        <option>Cloud Storage</option>
                        <option>CCTV Footage</option>
                        <option>Social Media Data</option>
                        <option>Other</option>
                    </select>
                </div>
                <div>
                    <label htmlFor="description" className="block text-sm font-medium text-slate-700 mb-1">Brief Description</label>
                    <textarea
                        name="description"
                        id="description"
                        value={evidenceInfo.description}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full p-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Describe what the evidence is, where it was collected from, and its general context in the case."
                    />
                </div>
            </div>
        </div>
    );
};

const AIAssistantPanel = ({ question, answer, onAnswerChange }: { question: Question, answer: string, onAnswerChange: (id: string, val: string) => void }) => {
    const [mode, setMode] = useState<'points' | 'critique' | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);

    const hasApiKey = process.env.API_KEY && process.env.API_KEY !== 'placeholder_api_key';

    const handleAction = async (actionType: 'points' | 'critique') => {
        if (!hasApiKey) {
            setError("API_KEY is not configured. Please set the environment variable to use the AI Assistant.");
            return;
        }
        if (actionType === 'critique' && !answer.trim()) {
            setError("Please write a draft answer before asking for a critique.");
            return;
        }

        setMode(actionType);
        setIsLoading(true);
        setError(null);
        setResult(null);

        try {
            let res;
            if (actionType === 'points') {
                res = await getAIKeyPoints(question);
            } else {
                res = await getAICritique(question, answer);
            }
            setResult(res);
        } catch (e: any) {
            setError(e.message || "An unknown error occurred.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleSuggestionClick = (suggestion: string) => {
        const separator = answer.trim() === '' ? '' : '\n\n';
        onAnswerChange(question.id, answer + separator + suggestion);
    };

    return (
        <div className="mt-4 p-4 bg-gray-800 border border-gray-700 rounded-lg">
            {error && <div className="status-danger text-sm mb-4 p-3 rounded-lg border">{error}</div>}

            {!isLoading && !result && (
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                        <SparklesIcon className="w-4 h-4 brand-primary" />
                        <p className="text-sm font-semibold text-white">AI Assistant:</p>
                    </div>
                    <button
                        onClick={() => handleAction('points')}
                        className="btn-ai px-3 py-1 text-sm transition-colors duration-200"
                    >
                        Suggest Key Points
                    </button>
                    <button
                        onClick={() => handleAction('critique')}
                        disabled={!answer.trim()}
                        className="btn-ai px-3 py-1 text-sm transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Critique My Draft
                    </button>
                </div>
            )}

            {isLoading && (
                <div className="text-center text-gray-400 py-4">
                    <div className="flex items-center justify-center gap-2">
                        <div className="w-4 h-4 border-2 border-brand-primary border-t-transparent rounded-full animate-spin"></div>
                        <span>AI is thinking...</span>
                    </div>
                </div>
            )}

            {result && mode === 'points' && (
                <div className="status-info rounded-lg p-4 border">
                    <h5 className="font-semibold text-white mb-3 flex items-center gap-2">
                        <LightbulbIcon className="w-4 h-4" />
                        Key Points to Consider:
                    </h5>
                    <ul className="space-y-2 text-sm">
                        {result.keyPoints.map((point: string, i: number) => (
                            <li key={i} className="flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                                <span>{point}</span>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
            
            {result && mode === 'critique' && (
                <div className="bg-gray-900 rounded-lg p-4 space-y-4">
                    <h5 className="font-semibold text-white mb-3 flex items-center gap-2">
                        <GavelIcon className="w-4 h-4 brand-primary" />
                        AI Critique:
                    </h5>

                    <div className="space-y-4">
                        {result.strengths.length > 0 && (
                            <div className="status-success rounded-lg p-3 border">
                                <h6 className="font-semibold mb-2">Strengths:</h6>
                                <ul className="space-y-1 text-sm">
                                    {result.strengths.map((strength: string, i: number) => (
                                        <li key={i} className="flex items-start gap-2">
                                            <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                                            <span>{strength}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        {result.weaknesses.length > 0 && (
                            <div className="status-danger rounded-lg p-3 border">
                                <h6 className="font-semibold mb-2">Areas for Improvement:</h6>
                                <ul className="space-y-1 text-sm">
                                    {result.weaknesses.map((weakness: string, i: number) => (
                                        <li key={i} className="flex items-start gap-2">
                                            <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                                            <span>{weakness}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        <div className="status-info rounded-lg p-3 border">
                            <h6 className="font-semibold mb-2">Recommendation:</h6>
                            <p className="text-sm mb-3">{result.recommendation}</p>
                            <button
                                onClick={() => handleSuggestionClick(result.recommendation)}
                                className="btn-primary px-3 py-1 text-xs rounded transition-colors duration-200"
                            >
                                Append to Answer
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {(result || error) && mode && (
                <button
                    onClick={() => { setResult(null); setError(null); setMode(null); }}
                    className="text-xs text-text-muted hover:text-text-secondary hover:underline mt-4 transition-colors"
                >
                    Reset Assistant
                </button>
            )}
        </div>
    );
};


const WizardStep = ({ question, answer, onAnswerChange }: { question: Question, answer: string, onAnswerChange: (id: string, val: string) => void }) => {
    const [isAssistantOpen, setAssistantOpen] = useState(false);

    return (
        <div>
            <h3 className="text-2xl font-bold text-slate-800">{question.title}</h3>
            <p className="text-slate-600 mt-2 mb-4">{question.text}</p>
            <textarea
                value={answer}
                onChange={(e) => onAnswerChange(question.id, e.target.value)}
                className="w-full h-48 p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-shadow placeholder:text-slate-500"
                placeholder="Provide a detailed response..."
            />
            <div className="mt-4 text-right">
                <button 
                    onClick={() => setAssistantOpen(prev => !prev)}
                    className="inline-flex items-center px-4 py-2 bg-slate-100 text-slate-800 font-semibold rounded-lg shadow-sm hover:bg-slate-200 transition-colors"
                >
                    <SparklesIcon /> {isAssistantOpen ? 'Close Assistant' : 'Ask AI Assistant'}
                </button>
            </div>
            {isAssistantOpen && <AIAssistantPanel question={question} answer={answer} onAnswerChange={onAnswerChange} />}
        </div>
    );
};


const FinalStep = ({ onGenerateReport }: { onGenerateReport: () => void }) => {
  return (
    <div>
      <h2 className="text-3xl font-bold text-center text-slate-800">Ready for Analysis</h2>
      <p className="text-center text-slate-600 mt-2 mb-8">You have answered all the questions. When you're ready, generate your comprehensive AI-powered analysis.</p>

      <div className="mt-8 text-center">
        <button
          onClick={onGenerateReport}
          className="inline-flex items-center justify-center px-8 py-3 bg-green-600 text-white font-bold text-lg rounded-lg shadow-lg hover:bg-green-700 transition-transform transform hover:scale-105"
        >
          <ReportIcon />
          Generate Comprehensive Report
        </button>
      </div>
      <div className="mt-6 text-center text-sm text-slate-500">
        <p>You can go back to previous steps to review or change your answers.</p>
      </div>
    </div>
  )
};

const LoadingSpinner = () => (
    <div className="card p-12 text-center">
        <div className="flex flex-col items-center justify-center">
            <div className="relative w-20 h-20 mb-6">
                <div className="absolute inset-0 border-4 border-gray-700 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-brand-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Generating Analysis</h3>
            <p className="text-sm text-gray-400">Our AI is analyzing your evidence against legal standards...</p>
            <div className="flex gap-2 mt-4">
                <div className="w-2 h-2 bg-brand-primary rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-brand-secondary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-brand-primary rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
        </div>
    </div>
);

const ErrorDisplay = ({ message, onRetry, isFinalStep }: { message: string, onRetry: () => void, isFinalStep: boolean }) => (
    <div className="card p-8 border-l-4 border-red-600" role="alert">
        <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
            </div>
            <div className="flex-1">
                <h3 className="text-lg font-semibold text-red-300 mb-2">Error Occurred</h3>
                <p className="text-sm text-gray-400 mb-4">{message}</p>
                {isFinalStep && (
                    <button
                        onClick={onRetry}
                        className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 font-semibold rounded-lg transition-colors duration-200"
                    >
                        Retry Generation
                    </button>
                )}
            </div>
        </div>
    </div>
);

const ConfidenceBadge = ({ confidence, large = false }: { confidence: 'High' | 'Medium' | 'Low' | undefined, large?: boolean }) => {
  let baseClasses = large
    ? "px-4 py-2 rounded-lg text-base font-bold"
    : "px-3 py-1 rounded-full text-xs font-semibold";
  let specificClasses = "";
  let text = "";

  switch (confidence) {
    case 'High':
      specificClasses = "status-success border";
      text = "High Confidence";
      break;
    case 'Medium':
      specificClasses = "status-warning border";
      text = "Medium Confidence";
      break;
    case 'Low':
      specificClasses = "status-danger border";
      text = "Low Confidence";
      break;
    default:
      return null;
  }
  return <span className={`${baseClasses} ${specificClasses}`}>{text}</span>;
};


const ConfidenceChart = ({ analysis }: { analysis: AnalysisResult }) => {
    const allFactors = analysis.analysisSections.flatMap(section => section.factorAnalyses);

    const getConfidenceClass = (confidence: 'High' | 'Medium' | 'Low') => {
        switch (confidence) {
            case 'High': return 'w-full bg-green-500';
            case 'Medium': return 'w-2/3 bg-yellow-500';
            case 'Low': return 'w-1/3 bg-red-500';
            default: return 'w-0';
        }
    };

    return (
        <div className="card p-6 mt-8">
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-3">
                <div className="w-8 h-8 bg-brand-primary rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                </div>
                Confidence Analysis
            </h3>
            <div className="space-y-6">
                {allFactors.map(factor => (
                    <div key={factor.factor} className="bg-gray-800 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                            <span className="text-white font-medium text-sm" title={factor.factor}>
                                {factor.factor}
                            </span>
                            <ConfidenceBadge confidence={factor.admissibilityConfidence} />
                        </div>
                        <div className="bg-gray-700 rounded-full h-2">
                            <div className={`h-2 rounded-full transition-all duration-500 ease-in-out ${getConfidenceClass(factor.admissibilityConfidence)}`}></div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

const ExecutiveSummaryView = ({ summary }: { summary: AnalysisResult['executiveSummary'] }) => {
    return (
        <div className="card p-8 mb-8">
            <h3 className="text-3xl font-bold text-white text-center mb-8">Executive Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-800 rounded-lg p-6 text-center hover:bg-gray-700 transition-colors duration-200">
                    <div className="w-12 h-12 bg-brand-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <h4 className="text-sm font-medium text-gray-300 mb-3">Overall Confidence</h4>
                    <ConfidenceBadge confidence={summary.overallConfidence} large={true} />
                </div>

                <div className="bg-gray-800 rounded-lg p-6 text-center hover:bg-gray-700 transition-colors duration-200">
                    <div className="w-12 h-12 bg-brand-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h4 className="text-sm font-medium text-gray-300 mb-4">Factor Breakdown</h4>
                    <div className="space-y-2">
                        <div className="flex items-center justify-between">
                            <span className="flex items-center text-green-300 text-sm">
                                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>High
                            </span>
                            <span className="text-green-300 font-bold">{summary.confidenceBreakdown.high}</span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="flex items-center text-yellow-300 text-sm">
                                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>Medium
                            </span>
                            <span className="text-yellow-300 font-bold">{summary.confidenceBreakdown.medium}</span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="flex items-center text-red-300 text-sm">
                                <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>Low
                            </span>
                            <span className="text-red-300 font-bold">{summary.confidenceBreakdown.low}</span>
                        </div>
                    </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-6 md:col-span-1 hover:bg-gray-700 transition-colors duration-200">
                    <div className="w-12 h-12 bg-brand-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                        <LightbulbIcon className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-sm font-medium text-gray-300 mb-4 text-center">Critical Actions</h4>
                    <div className="text-sm text-gray-400">
                        {summary.topRecommendations.slice(0, 2).map((rec, index) => (
                            <div key={index} className="flex items-start gap-2 mb-2">
                                <div className="w-1.5 h-1.5 bg-brand-primary rounded-full mt-2 flex-shrink-0"></div>
                                <span>{rec}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className="mt-6 bg-gray-800 rounded-lg p-6">
                <h4 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                    <LightbulbIcon className="w-5 h-5 brand-primary" />
                    Top Recommendations
                </h4>
                <ol className="space-y-3 text-sm text-gray-400">
                    {summary.topRecommendations.map((rec, index) => (
                        <li key={index} className="flex items-start gap-3">
                            <span className="w-6 h-6 bg-brand-primary rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0">
                                {index + 1}
                            </span>
                            <span>{rec}</span>
                        </li>
                    ))}
                </ol>
            </div>
        </div>
    )
}


const ReportView = ({ analysis, answers, evidenceInfo }: { analysis: AnalysisResult, answers: Answer[], evidenceInfo: EvidenceInfo }) => {
  const getAnswerForFactor = (factorName: string) => {
    const question = EVIDENCE_QUESTIONS.find(q => q.factor === factorName);
    return answers.find(a => a.questionId === question?.id)?.value || "Not provided.";
  };
  
  const downloadReport = () => {
    const reportElement = document.getElementById('report-content');
    if (!reportElement) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <title>Digital Evidence Assessment Report</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style> body { font-family: sans-serif; } .no-print { display: none; } </style>
      </head>
      <body class="bg-slate-50 p-8">
        <div class="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-lg">
          ${reportElement.innerHTML.replace(/<svg.*?>.*?<\/svg>/g, '')}
        </div>
      </body>
      </html>
    `;
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'Digital_Evidence_Assessment_Report.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-8">
       <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-white">Analysis Report</h2>
          <p className="text-sm text-gray-400 mt-1">Comprehensive legal admissibility assessment</p>
        </div>
        <button
          onClick={downloadReport}
          className="btn-primary flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors duration-200 no-print"
        >
            <DownloadIcon className="w-4 h-4" />
            Download Report
        </button>
       </div>

      <div id="report-content">
        {analysis.executiveSummary && <ExecutiveSummaryView summary={analysis.executiveSummary} />}

        <div className="card p-6 border-l-4 border-blue-600">
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <ShieldExclamationIcon className="w-4 h-4 text-white" />
                </div>
                Evidence Under Review
            </h3>
            <dl className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-800 rounded-lg p-4">
                    <dt className="text-sm font-medium text-gray-300 mb-1">Identifier</dt>
                    <dd className="text-white font-medium">{evidenceInfo.name || 'N/A'}</dd>
                </div>
                <div className="bg-gray-800 rounded-lg p-4">
                    <dt className="text-sm font-medium text-gray-300 mb-1">Type</dt>
                    <dd className="text-white font-medium">{evidenceInfo.type}</dd>
                </div>
                <div className="bg-gray-800 rounded-lg p-4 md:col-span-1">
                    <dt className="text-sm font-medium text-gray-300 mb-1">Description</dt>
                    <dd className="text-white whitespace-pre-wrap">{evidenceInfo.description || 'N/A'}</dd>
                </div>
            </dl>
        </div>

        <div className="card p-6 border-l-4 border-green-600">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                    <GavelIcon className="w-4 h-4 text-white" />
                </div>
                Overall Conclusion
            </h3>
            <div className="bg-gray-800 rounded-lg p-4">
                <p className="text-sm text-gray-400 leading-relaxed">{analysis.overallConclusion}</p>
            </div>
        </div>

        <ConfidenceChart analysis={analysis} />

        {analysis.analysisSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="mt-10">
                <div className="bg-slate-100 p-3 rounded-t-lg border-b-4 border-slate-300">
                    <h3 className="text-2xl font-bold text-slate-900">{section.sectionTitle}</h3>
                </div>
                <div className="bg-slate-50 p-4 rounded-b-lg">
                    <p className="text-slate-700 italic">{section.sectionSummary}</p>
                </div>
                
                {section.factorAnalyses.map((factorAnalysis) => (
                    <div key={factorAnalysis.factor} className="mt-8 pt-6 border-t-2 border-dashed">
                        <div className="flex items-center">
                            <h4 className="text-xl font-bold text-blue-700">{factorAnalysis.factor}</h4>
                            <ConfidenceBadge confidence={factorAnalysis.admissibilityConfidence} />
                        </div>
                        
                        <div className="mt-4 p-4 bg-slate-50 rounded-lg border">
                            <h5 className="font-semibold text-slate-800">Your Response:</h5>
                            <p className="text-sm mt-1 whitespace-pre-wrap text-slate-700">{getAnswerForFactor(factorAnalysis.factor)}</p>
                        </div>

                        <div className="mt-4">
                            <h5 className="font-semibold text-slate-800">AI Summary:</h5>
                            <p className="mt-2 text-slate-700">{factorAnalysis.summary}</p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                                <h5 className="font-semibold text-green-800">Strengths</h5>
                                <ul className="list-disc list-inside mt-2 space-y-1 text-green-900">
                                    {factorAnalysis.strengths.map((item, index) => <li key={index}>{item}</li>)}
                                </ul>
                            </div>
                            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                                <h5 className="font-semibold text-yellow-800">Weaknesses / Areas for Improvement</h5>
                                <ul className="list-disc list-inside mt-2 space-y-1 text-yellow-900">
                                    {factorAnalysis.weaknesses.map((item, index) => <li key={index}>{item}</li>)}
                                </ul>
                            </div>
                        </div>

                        <div className="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <h5 className="font-semibold text-blue-800 flex items-center"><LightbulbIcon />Actionable Recommendations</h5>
                            <ul className="list-disc list-inside mt-2 space-y-1 text-blue-900 pl-4">
                                {factorAnalysis.actionableRecommendations.map((item, index) => <li key={index}>{item}</li>)}
                            </ul>
                        </div>
                        
                        <div className="mt-6 bg-indigo-50 p-4 rounded-lg border border-indigo-200">
                            <h5 className="font-semibold text-indigo-800 flex items-center"><GavelIcon />Potential Cross-Examination Questions</h5>
                            <ul className="list-disc list-inside mt-2 space-y-1 text-indigo-900 pl-4">
                                {factorAnalysis.crossExaminationQuestions.map((item, index) => <li key={index}>{item}</li>)}
                            </ul>
                        </div>

                        <div className="mt-6 bg-orange-50 p-4 rounded-lg border border-orange-200">
                            <h5 className="font-semibold text-orange-800 flex items-center"><ShieldExclamationIcon />Recommended Suppression Arguments</h5>
                            <ul className="list-disc list-inside mt-2 space-y-1 text-orange-900 pl-4">
                                {factorAnalysis.recommendedSuppressionText.map((item, index) => <li key={index}>{item}</li>)}
                            </ul>
                        </div>

                    </div>
                ))}
            </div>
        ))}
      </div>
    </div>
  );
};


export default App;