import React, { useState, useCallback, useMemo } from 'react';
import { Answer, AnalysisResult, Question } from './types';
import { EVIDENCE_QUESTIONS } from './constants';
import { generateComprehensiveAnalysis, getAIKeyPoints, getAICritique } from './services/geminiService';
import ProgressBar from './components/ProgressBar';
import { ChevronLeftIcon, ChevronRightIcon, DownloadIcon, ReportIcon, LightbulbIcon, GavelIcon, ShieldExclamationIcon, SparklesIcon } from './components/icons';

interface EvidenceInfo {
  name: string;
  description: string;
  type: string;
}

const App: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [evidenceInfo, setEvidenceInfo] = useState<EvidenceInfo>({
    name: '',
    description: '',
    type: 'Mobile Phone',
  });
  const [answers, setAnswers] = useState<Answer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const totalQuestions = EVIDENCE_QUESTIONS.length;
  const totalSteps = totalQuestions + 1; // +1 for the evidence definition step
  const isFinalStep = currentStep === totalSteps;
  const currentQuestion = currentStep > 0 && currentStep <= totalQuestions ? EVIDENCE_QUESTIONS[currentStep - 1] : null;
  
  const handleAnswerChange = useCallback((questionId: string, value: string) => {
    setAnswers(prevAnswers => {
      const existingAnswerIndex = prevAnswers.findIndex(a => a.questionId === questionId);
      if (existingAnswerIndex > -1) {
        const newAnswers = [...prevAnswers];
        newAnswers[existingAnswerIndex] = { questionId, value };
        return newAnswers;
      }
      return [...prevAnswers, { questionId, value }];
    });
  }, []);

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const handleGenerateReport = async () => {
    setIsLoading(true);
    setError(null);
    setAnalysis(null);
    try {
      if (!process.env.API_KEY || process.env.API_KEY === 'placeholder_api_key') {
          throw new Error("API_KEY is not configured. Please set the environment variable to generate a report.");
      }
      const result = await generateComprehensiveAnalysis(answers, evidenceInfo);
      setAnalysis(result);
    } catch (e) {
      if (e instanceof Error) {
        setError(e.message);
      } else {
        setError('An unknown error occurred.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentStep = () => {
    if (analysis) return <ReportView analysis={analysis} answers={answers} evidenceInfo={evidenceInfo} />;
    if (isLoading) return <LoadingSpinner />;
    if (error) return <ErrorDisplay message={error} onRetry={handleGenerateReport} isFinalStep={isFinalStep} />;
    
    if (currentStep === 0) {
        return <EvidenceDefinitionStep evidenceInfo={evidenceInfo} setEvidenceInfo={setEvidenceInfo} />
    }

    if (currentStep > 0 && !isFinalStep && currentQuestion) {
      const answer = answers.find(a => a.questionId === currentQuestion.id)?.value || '';
      return <WizardStep question={currentQuestion} answer={answer} onAnswerChange={handleAnswerChange} />;
    }
    
    if (isFinalStep) {
      return <FinalStep onGenerateReport={handleGenerateReport} />;
    }

    return null;
  };
  
  const handleStartOver = () => {
    setCurrentStep(0);
    setEvidenceInfo({ name: '', description: '', type: 'Mobile Phone' });
    setAnswers([]);
    setAnalysis(null);
    setError(null);
    setIsLoading(false);
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-4xl mx-auto">
        <header className="text-center mb-8">
            <h1 className="text-4xl font-bold text-slate-800">Digital Evidence Assessor</h1>
            <p className="text-slate-600 mt-2">Assess your digital evidence for relevance, authenticity, reliability, and Daubert admissibility.</p>
        </header>

        <main className="bg-white rounded-xl shadow-lg p-6 sm:p-8 lg:p-10 transition-all duration-300">
            {!analysis && currentStep > 0 && currentStep <= totalQuestions && (
              <>
                <h2 className="text-xl font-semibold text-slate-700 text-center mb-2">{currentQuestion?.section}</h2>
                <ProgressBar currentStep={currentStep - 1} totalSteps={totalQuestions} />
              </>
            )}
            
            {renderCurrentStep()}
            
            {!analysis && !error && (
              <div className="mt-8 flex justify-between items-center">
                <button
                  onClick={handlePrev}
                  disabled={currentStep === 0 || isLoading}
                  className="flex items-center px-4 py-2 bg-slate-200 text-slate-700 font-semibold rounded-lg shadow-sm hover:bg-slate-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ChevronLeftIcon />
                  <span className="ml-2">Previous</span>
                </button>
                {!isFinalStep && (
                  <button
                    onClick={handleNext}
                    disabled={isLoading}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <span className="mr-2">{currentStep === 0 ? "Start Assessment" : "Next"}</span>
                    <ChevronRightIcon />
                  </button>
                )}
              </div>
            )}
            
            {(analysis || error) && (
                <div className="mt-8 text-center">
                    <button
                        onClick={handleStartOver}
                        className="px-6 py-2 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 transition-colors"
                    >
                        Start Over
                    </button>
                </div>
            )}
        </main>
      </div>
    </div>
  );
};


const EvidenceDefinitionStep = ({ evidenceInfo, setEvidenceInfo }: { evidenceInfo: EvidenceInfo, setEvidenceInfo: (info: EvidenceInfo) => void }) => {
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setEvidenceInfo({ ...evidenceInfo, [name]: value });
    };

    return (
        <div>
            <h2 className="text-3xl font-bold text-slate-800 text-center">Define Your Evidence</h2>
            <p className="text-slate-600 mt-2 mb-6 text-center">First, provide some basic details about the digital evidence you are assessing.</p>
            <div className="space-y-4">
                <div>
                    <label htmlFor="name" className="block text-sm font-medium text-slate-700 mb-1">Evidence Name / Identifier</label>
                    <input
                        type="text"
                        name="name"
                        id="name"
                        value={evidenceInfo.name}
                        onChange={handleInputChange}
                        className="w-full p-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., iPhone 13 from suspect's vehicle"
                    />
                </div>
                <div>
                    <label htmlFor="type" className="block text-sm font-medium text-slate-700 mb-1">Type of Evidence</label>
                    <select
                        name="type"
                        id="type"
                        value={evidenceInfo.type}
                        onChange={handleInputChange}
                        className="w-full p-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                        <option>Mobile Phone</option>
                        <option>Laptop / Computer</option>
                        <option>Email Account</option>
                        <option>Cloud Storage</option>
                        <option>CCTV Footage</option>
                        <option>Social Media Data</option>
                        <option>Other</option>
                    </select>
                </div>
                <div>
                    <label htmlFor="description" className="block text-sm font-medium text-slate-700 mb-1">Brief Description</label>
                    <textarea
                        name="description"
                        id="description"
                        value={evidenceInfo.description}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full p-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Describe what the evidence is, where it was collected from, and its general context in the case."
                    />
                </div>
            </div>
        </div>
    );
};

const AIAssistantPanel = ({ question, answer, onAnswerChange }: { question: Question, answer: string, onAnswerChange: (id: string, val: string) => void }) => {
    const [mode, setMode] = useState<'points' | 'critique' | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);

    const hasApiKey = process.env.API_KEY && process.env.API_KEY !== 'placeholder_api_key';

    const handleAction = async (actionType: 'points' | 'critique') => {
        if (!hasApiKey) {
            setError("API_KEY is not configured. Please set the environment variable to use the AI Assistant.");
            return;
        }
        if (actionType === 'critique' && !answer.trim()) {
            setError("Please write a draft answer before asking for a critique.");
            return;
        }

        setMode(actionType);
        setIsLoading(true);
        setError(null);
        setResult(null);

        try {
            let res;
            if (actionType === 'points') {
                res = await getAIKeyPoints(question);
            } else {
                res = await getAICritique(question, answer);
            }
            setResult(res);
        } catch (e: any) {
            setError(e.message || "An unknown error occurred.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleSuggestionClick = (suggestion: string) => {
        const separator = answer.trim() === '' ? '' : '\n\n';
        onAnswerChange(question.id, answer + separator + suggestion);
    };

    return (
        <div className="mt-4 p-4 bg-slate-50 border border-slate-200 rounded-lg">
            {error && <div className="text-red-600 text-sm mb-4">{error}</div>}
            
            {!isLoading && !result && (
                <div className="flex items-center gap-4">
                    <p className="text-sm font-semibold text-slate-700">AI Assistant:</p>
                    <button onClick={() => handleAction('points')} className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">Suggest Key Points</button>
                    <button onClick={() => handleAction('critique')} disabled={!answer.trim()} className="px-3 py-1 text-sm bg-indigo-100 text-indigo-800 rounded-md hover:bg-indigo-200 disabled:opacity-50 disabled:cursor-not-allowed">Critique My Draft</button>
                </div>
            )}

            {isLoading && <div className="text-center text-slate-600">Thinking...</div>}

            {result && mode === 'points' && (
                <div>
                    <h5 className="font-semibold text-slate-800">Key Points to Consider:</h5>
                    <ul className="list-disc list-inside mt-2 space-y-1 text-slate-700 text-sm">
                        {result.keyPoints.map((point: string, i: number) => <li key={i}>{point}</li>)}
                    </ul>
                </div>
            )}
            
            {result && mode === 'critique' && (
                <div>
                    <h5 className="font-semibold text-slate-800 mb-2">Critique of Your Draft:</h5>
                    {result.strengths.length > 0 && (
                        <div className="mb-2">
                            <h6 className="font-semibold text-green-700">Strengths:</h6>
                            <ul className="list-disc list-inside text-sm text-green-800">
                                {result.strengths.map((s: string, i: number) => <li key={i}>{s}</li>)}
                            </ul>
                        </div>
                    )}
                    {result.weaknesses.length > 0 && (
                         <div className="mb-2">
                            <h6 className="font-semibold text-yellow-700">Weaknesses:</h6>
                            <ul className="list-disc list-inside text-sm text-yellow-800">
                                {result.weaknesses.map((w: string, i: number) => <li key={i}>{w}</li>)}
                            </ul>
                        </div>
                    )}
                     <div>
                        <h6 className="font-semibold text-blue-700">Recommendation:</h6>
                        <p className="text-sm text-blue-800">{result.recommendation}</p>
                        <button onClick={() => handleSuggestionClick(result.recommendation)} className="mt-2 px-2 py-1 text-xs bg-blue-100 rounded hover:bg-blue-200">Append Recommendation</button>
                    </div>
                </div>
            )}

            {(result || error) && mode && (
                <button onClick={() => { setResult(null); setError(null); setMode(null); }} className="text-xs text-slate-500 hover:underline mt-4">Reset Assistant</button>
            )}
        </div>
    );
};


const WizardStep = ({ question, answer, onAnswerChange }: { question: Question, answer: string, onAnswerChange: (id: string, val: string) => void }) => {
    const [isAssistantOpen, setAssistantOpen] = useState(false);

    return (
        <div>
            <h3 className="text-2xl font-bold text-slate-800">{question.title}</h3>
            <p className="text-slate-600 mt-2 mb-4">{question.text}</p>
            <textarea
                value={answer}
                onChange={(e) => onAnswerChange(question.id, e.target.value)}
                className="w-full h-48 p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-shadow placeholder:text-slate-500"
                placeholder="Provide a detailed response..."
            />
            <div className="mt-4 text-right">
                <button 
                    onClick={() => setAssistantOpen(prev => !prev)}
                    className="inline-flex items-center px-4 py-2 bg-slate-100 text-slate-800 font-semibold rounded-lg shadow-sm hover:bg-slate-200 transition-colors"
                >
                    <SparklesIcon /> {isAssistantOpen ? 'Close Assistant' : 'Ask AI Assistant'}
                </button>
            </div>
            {isAssistantOpen && <AIAssistantPanel question={question} answer={answer} onAnswerChange={onAnswerChange} />}
        </div>
    );
};


const FinalStep = ({ onGenerateReport }: { onGenerateReport: () => void }) => {
  return (
    <div>
      <h2 className="text-3xl font-bold text-center text-slate-800">Ready for Analysis</h2>
      <p className="text-center text-slate-600 mt-2 mb-8">You have answered all the questions. When you're ready, generate your comprehensive AI-powered analysis.</p>

      <div className="mt-8 text-center">
        <button
          onClick={onGenerateReport}
          className="inline-flex items-center justify-center px-8 py-3 bg-green-600 text-white font-bold text-lg rounded-lg shadow-lg hover:bg-green-700 transition-transform transform hover:scale-105"
        >
          <ReportIcon />
          Generate Comprehensive Report
        </button>
      </div>
      <div className="mt-6 text-center text-sm text-slate-500">
        <p>You can go back to previous steps to review or change your answers.</p>
      </div>
    </div>
  )
};

const LoadingSpinner = () => (
    <div className="flex flex-col items-center justify-center min-h-[300px]">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-slate-600 text-lg font-semibold">Generating your analysis... This may take a moment.</p>
    </div>
);

const ErrorDisplay = ({ message, onRetry, isFinalStep }: { message: string, onRetry: () => void, isFinalStep: boolean }) => (
    <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert">
        <p className="font-bold">Error</p>
        <p>{message}</p>
        {isFinalStep && (
             <button onClick={onRetry} className="mt-4 px-4 py-2 bg-red-600 text-white font-semibold rounded-lg shadow-md hover:bg-red-700 transition-colors">
                Retry Generation
             </button>
        )}
    </div>
);

const ConfidenceBadge = ({ confidence, large = false }: { confidence: 'High' | 'Medium' | 'Low' | undefined, large?: boolean }) => {
  let baseClasses = large 
    ? "px-4 py-2 rounded-lg text-base font-bold" 
    : "ml-3 px-3 py-1 rounded-full text-xs font-bold tracking-wider uppercase";
  let specificClasses = "";
  let text = "";

  switch (confidence) {
    case 'High':
      specificClasses = "bg-green-100 text-green-800";
      text = "High Confidence";
      break;
    case 'Medium':
      specificClasses = "bg-yellow-100 text-yellow-800";
      text = "Medium Confidence";
      break;
    case 'Low':
      specificClasses = "bg-red-100 text-red-800";
      text = "Low Confidence";
      break;
    default:
      return null;
  }
  return <span className={`${baseClasses} ${specificClasses}`}>{text}</span>;
};


const ConfidenceChart = ({ analysis }: { analysis: AnalysisResult }) => {
    const allFactors = analysis.analysisSections.flatMap(section => section.factorAnalyses);

    const getConfidenceClass = (confidence: 'High' | 'Medium' | 'Low') => {
        switch (confidence) {
            case 'High': return 'w-full bg-green-500';
            case 'Medium': return 'w-2/3 bg-yellow-500';
            case 'Low': return 'w-1/3 bg-red-500';
            default: return 'w-0';
        }
    };
    
    return (
        <div className="mt-10 p-4 bg-slate-100 rounded-lg">
            <h3 className="text-2xl font-semibold border-b pb-2 text-slate-800 mb-4">Admissibility Confidence Overview</h3>
            <div className="space-y-3">
                {allFactors.map(factor => (
                    <div key={factor.factor} className="grid grid-cols-3 items-center gap-4">
                        <span className="text-slate-700 font-medium col-span-1 truncate" title={factor.factor}>{factor.factor}</span>
                        <div className="col-span-2 bg-slate-200 rounded-full h-4">
                            <div className={`h-4 rounded-full transition-all duration-500 ease-in-out ${getConfidenceClass(factor.admissibilityConfidence)}`}></div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

const ExecutiveSummaryView = ({ summary }: { summary: AnalysisResult['executiveSummary'] }) => {
    return (
        <div className="p-6 bg-slate-50 rounded-xl border-2 border-slate-200 mb-10">
            <h3 className="text-2xl font-bold text-slate-900 text-center mb-6">Executive Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-sm">
                    <h4 className="font-semibold text-slate-700 mb-3">Overall Confidence</h4>
                    <ConfidenceBadge confidence={summary.overallConfidence} large={true} />
                </div>
                 <div className="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-sm">
                    <h4 className="font-semibold text-slate-700 mb-3">Factor Breakdown</h4>
                    <div className="flex space-x-4">
                        <span className="flex items-center text-green-700 font-bold"><div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>High: {summary.confidenceBreakdown.high}</span>
                        <span className="flex items-center text-yellow-700 font-bold"><div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>Med: {summary.confidenceBreakdown.medium}</span>
                        <span className="flex items-center text-red-700 font-bold"><div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>Low: {summary.confidenceBreakdown.low}</span>
                    </div>
                </div>
                <div className="p-4 bg-white rounded-lg shadow-sm md:col-span-3">
                    <h4 className="font-semibold text-slate-700 mb-2 flex items-center"><LightbulbIcon />Top 3 Critical Recommendations</h4>
                    <ol className="list-decimal list-inside space-y-2 text-slate-800">
                        {summary.topRecommendations.map((rec, index) => <li key={index}>{rec}</li>)}
                    </ol>
                </div>
            </div>
        </div>
    )
}


const ReportView = ({ analysis, answers, evidenceInfo }: { analysis: AnalysisResult, answers: Answer[], evidenceInfo: EvidenceInfo }) => {
  const getAnswerForFactor = (factorName: string) => {
    const question = EVIDENCE_QUESTIONS.find(q => q.factor === factorName);
    return answers.find(a => a.questionId === question?.id)?.value || "Not provided.";
  };
  
  const downloadReport = () => {
    const reportElement = document.getElementById('report-content');
    if (!reportElement) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <title>Digital Evidence Assessment Report</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style> body { font-family: sans-serif; } .no-print { display: none; } </style>
      </head>
      <body class="bg-slate-50 p-8">
        <div class="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-lg">
          ${reportElement.innerHTML.replace(/<svg.*?>.*?<\/svg>/g, '')}
        </div>
      </body>
      </html>
    `;
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'Digital_Evidence_Assessment_Report.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div>
       <div className="flex justify-between items-center mb-6">
        <h2 className="text-3xl font-bold text-slate-800">Analysis Report</h2>
        <button onClick={downloadReport} className="flex items-center px-4 py-2 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-700 transition-colors no-print">
            <DownloadIcon />
            Download HTML
        </button>
       </div>

      <div id="report-content">
        {analysis.executiveSummary && <ExecutiveSummaryView summary={analysis.executiveSummary} />}

        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-2xl font-semibold border-b pb-2 text-blue-900">Evidence Under Review</h3>
            <dl className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-2 text-blue-800">
                <div className="md:col-span-1"><dt className="font-bold">Identifier:</dt><dd className="pl-2">{evidenceInfo.name || 'N/A'}</dd></div>
                <div className="md:col-span-1"><dt className="font-bold">Type:</dt><dd className="pl-2">{evidenceInfo.type}</dd></div>
                <div className="md:col-span-3"><dt className="font-bold">Description:</dt><dd className="pl-2 whitespace-pre-wrap">{evidenceInfo.description || 'N/A'}</dd></div>
            </dl>
        </div>

        <div className="mt-10 p-4 bg-gray-100 rounded-lg">
            <h3 className="text-2xl font-semibold border-b pb-2 text-slate-800">Overall Conclusion</h3>
            <p className="mt-4 text-slate-700">{analysis.overallConclusion}</p>
        </div>

        <ConfidenceChart analysis={analysis} />

        {analysis.analysisSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="mt-10">
                <div className="bg-slate-100 p-3 rounded-t-lg border-b-4 border-slate-300">
                    <h3 className="text-2xl font-bold text-slate-900">{section.sectionTitle}</h3>
                </div>
                <div className="bg-slate-50 p-4 rounded-b-lg">
                    <p className="text-slate-700 italic">{section.sectionSummary}</p>
                </div>
                
                {section.factorAnalyses.map((factorAnalysis) => (
                    <div key={factorAnalysis.factor} className="mt-8 pt-6 border-t-2 border-dashed">
                        <div className="flex items-center">
                            <h4 className="text-xl font-bold text-blue-700">{factorAnalysis.factor}</h4>
                            <ConfidenceBadge confidence={factorAnalysis.admissibilityConfidence} />
                        </div>
                        
                        <div className="mt-4 p-4 bg-slate-50 rounded-lg border">
                            <h5 className="font-semibold text-slate-800">Your Response:</h5>
                            <p className="text-sm mt-1 whitespace-pre-wrap text-slate-700">{getAnswerForFactor(factorAnalysis.factor)}</p>
                        </div>

                        <div className="mt-4">
                            <h5 className="font-semibold text-slate-800">AI Summary:</h5>
                            <p className="mt-2 text-slate-700">{factorAnalysis.summary}</p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                                <h5 className="font-semibold text-green-800">Strengths</h5>
                                <ul className="list-disc list-inside mt-2 space-y-1 text-green-900">
                                    {factorAnalysis.strengths.map((item, index) => <li key={index}>{item}</li>)}
                                </ul>
                            </div>
                            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                                <h5 className="font-semibold text-yellow-800">Weaknesses / Areas for Improvement</h5>
                                <ul className="list-disc list-inside mt-2 space-y-1 text-yellow-900">
                                    {factorAnalysis.weaknesses.map((item, index) => <li key={index}>{item}</li>)}
                                </ul>
                            </div>
                        </div>

                        <div className="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <h5 className="font-semibold text-blue-800 flex items-center"><LightbulbIcon />Actionable Recommendations</h5>
                            <ul className="list-disc list-inside mt-2 space-y-1 text-blue-900 pl-4">
                                {factorAnalysis.actionableRecommendations.map((item, index) => <li key={index}>{item}</li>)}
                            </ul>
                        </div>
                        
                        <div className="mt-6 bg-indigo-50 p-4 rounded-lg border border-indigo-200">
                            <h5 className="font-semibold text-indigo-800 flex items-center"><GavelIcon />Potential Cross-Examination Questions</h5>
                            <ul className="list-disc list-inside mt-2 space-y-1 text-indigo-900 pl-4">
                                {factorAnalysis.crossExaminationQuestions.map((item, index) => <li key={index}>{item}</li>)}
                            </ul>
                        </div>

                        <div className="mt-6 bg-orange-50 p-4 rounded-lg border border-orange-200">
                            <h5 className="font-semibold text-orange-800 flex items-center"><ShieldExclamationIcon />Recommended Suppression Arguments</h5>
                            <ul className="list-disc list-inside mt-2 space-y-1 text-orange-900 pl-4">
                                {factorAnalysis.recommendedSuppressionText.map((item, index) => <li key={index}>{item}</li>)}
                            </ul>
                        </div>

                    </div>
                ))}
            </div>
        ))}
      </div>
    </div>
  );
};


export default App;